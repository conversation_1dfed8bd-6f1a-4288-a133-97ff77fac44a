<svg width="120" height="40" viewBox="0 0 120 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="20" cy="20" r="18" fill="url(#gradient)" stroke="currentColor" stroke-width="2"/>
  
  <!-- Inner design -->
  <path d="M12 20L18 14L24 20L18 26L12 20Z" fill="white" opacity="0.9"/>
  <circle cx="18" cy="20" r="3" fill="currentColor"/>
  
  <!-- Company name -->
  <text x="45" y="16" font-family="system-ui, sans-serif" font-size="14" font-weight="700" fill="currentColor">
    Your Company
  </text>
  <text x="45" y="28" font-family="system-ui, sans-serif" font-size="8" font-weight="400" fill="currentColor" opacity="0.7">
    Professional Solutions
  </text>
  
  <!-- Gradient definition -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
