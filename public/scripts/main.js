// Custom JavaScript for your Astro template

// Wait for DOM to be fully loaded
document.addEventListener("DOMContentLoaded", function () {
    console.log("Astro template loaded successfully!");

    // Initialize your custom functionality here
    initMobileMenu();
    initSmoothScrolling();
    initFormValidation();
});

// Mobile menu functionality
function initMobileMenu() {
    const mobileMenuButton = document.querySelector("#mobile-menu-button");
    const mobileMenu = document.querySelector("#mobile-menu");

    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener("click", function () {
            const isHidden = mobileMenu.classList.contains("hidden");

            if (isHidden) {
                mobileMenu.classList.remove("hidden");
                mobileMenu.classList.add("animate-fadeIn");
            } else {
                mobileMenu.classList.add("hidden");
                mobileMenu.classList.remove("animate-fadeIn");
            }

            // Update button icon (optional enhancement)
            const icon = mobileMenuButton.querySelector("svg");
            if (icon) {
                icon.style.transform = isHidden
                    ? "rotate(90deg)"
                    : "rotate(0deg)";
            }
        });

        // Close mobile menu when clicking outside
        document.addEventListener("click", function (event) {
            if (
                !mobileMenuButton.contains(event.target) &&
                !mobileMenu.contains(event.target)
            ) {
                mobileMenu.classList.add("hidden");
                mobileMenu.classList.remove("animate-fadeIn");
            }
        });
    }
}

// Smooth scrolling for anchor links
function initSmoothScrolling() {
    const anchorLinks = document.querySelectorAll('a[href^="#"]');

    anchorLinks.forEach((link) => {
        link.addEventListener("click", function (e) {
            const targetId = this.getAttribute("href");
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                e.preventDefault();
                targetElement.scrollIntoView({
                    behavior: "smooth",
                    block: "start",
                });
            }
        });
    });
}

// Basic form validation
function initFormValidation() {
    const forms = document.querySelectorAll("form");

    forms.forEach((form) => {
        form.addEventListener("submit", function (e) {
            const requiredFields = form.querySelectorAll("[required]");
            let isValid = true;

            requiredFields.forEach((field) => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add("border-red-500");

                    // Remove error styling when user starts typing
                    field.addEventListener("input", function () {
                        this.classList.remove("border-red-500");
                    });
                } else {
                    field.classList.remove("border-red-500");
                }
            });

            if (!isValid) {
                e.preventDefault();
                alert("Please fill in all required fields.");
            }
        });
    });
}

// Utility function: Add fade-in animation to elements
function addFadeInAnimation(selector) {
    const elements = document.querySelectorAll(selector);

    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
            if (entry.isIntersecting) {
                entry.target.classList.add("fade-in");
                observer.unobserve(entry.target);
            }
        });
    });

    elements.forEach((element) => {
        observer.observe(element);
    });
}

// Example usage: Add fade-in animation to sections
// addFadeInAnimation('section');

// Add your custom JavaScript functions below this line
