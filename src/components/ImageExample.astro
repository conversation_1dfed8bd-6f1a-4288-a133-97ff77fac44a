---
// Example component showing how to use Astro's Image component
// Uncomment and use this pattern when you add real JPG images

// import { Image } from 'astro:assets';
// import myImage from '../assets/images/my-image.jpg';

export interface Props {
  src?: string;
  alt: string;
  width?: number;
  height?: number;
  class?: string;
}

const {
  src = '/images/placeholder-800x600.svg',
  alt,
  width = 800,
  height = 600,
  class: className = '',
} = Astro.props;
---

<!-- Current implementation using regular img tag -->
<img 
  src={src}
  alt={alt}
  width={width}
  height={height}
  class={className}
  loading="lazy"
  decoding="async"
/>

<!-- 
When you add real JPG images, replace the above with:

<Image 
  src={myImage}
  alt={alt}
  width={width}
  height={height}
  class={className}
/>

Benefits of Astro's Image component:
- Automatic format conversion (WebP, AVIF)
- Responsive image generation
- Lazy loading by default
- Build-time optimization
- Automatic width/height attributes
- Better performance and Core Web Vitals scores
-->
