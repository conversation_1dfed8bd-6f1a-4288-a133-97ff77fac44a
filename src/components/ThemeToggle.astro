---
// Theme toggle component for dark/light mode switching
---

<button
  class="theme-toggle relative inline-flex items-center justify-center w-10 h-10 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900"
  type="button"
  aria-label="Toggle theme"
>
  <!-- Sun icon (visible in dark mode) -->
  <svg
    id="theme-toggle-light-icon"
    class="w-5 h-5 text-yellow-500 hidden dark:block"
    fill="currentColor"
    viewBox="0 0 20 20"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
      clip-rule="evenodd"
    ></path>
  </svg>
  
  <!-- Moon icon (visible in light mode) -->
  <svg
    id="theme-toggle-dark-icon"
    class="w-5 h-5 text-gray-700 block dark:hidden"
    fill="currentColor"
    viewBox="0 0 20 20"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"
    ></path>
  </svg>
</button>

<script>
  // Theme toggle functionality - works with multiple toggle buttons
  const html = document.documentElement;

  // Check for saved theme preference or default to 'light'
  const currentTheme = localStorage.getItem('theme') || 'light';

  // Apply the current theme
  if (currentTheme === 'dark') {
    html.classList.add('dark');
  } else {
    html.classList.remove('dark');
  }

  // Toggle theme function
  function toggleTheme() {
    if (html.classList.contains('dark')) {
      html.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    } else {
      html.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    }
  }

  // Add event listeners to all theme toggle buttons
  document.addEventListener('DOMContentLoaded', function() {
    const themeToggleButtons = document.querySelectorAll('.theme-toggle');
    themeToggleButtons.forEach(button => {
      button.addEventListener('click', toggleTheme);
    });
  });

  // Handle system theme changes
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

  function handleSystemThemeChange(e: any) {
    if (!localStorage.getItem('theme')) {
      if (e.matches) {
        html.classList.add('dark');
      } else {
        html.classList.remove('dark');
      }
    }
  }

  mediaQuery.addEventListener('change', handleSystemThemeChange);

  // Initialize based on system preference if no saved preference
  if (!localStorage.getItem('theme')) {
    if (mediaQuery.matches) {
      html.classList.add('dark');
    }
  }
</script>
