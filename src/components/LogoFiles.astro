---
// Alternative Logo component using separate SVG files
// This approach is easier for designers who prefer working with separate files

export interface Props {
  class?: string;
  width?: string;
  height?: string;
}

const { class: className = "", width = "120", height = "40" } = Astro.props;
---

<div class={`inline-flex items-center ${className}`}>
  <!-- Light theme logo -->
  <img 
    src="/images/logo-light.svg" 
    alt="Your Company Logo"
    class="block dark:hidden transition-opacity duration-200"
    width={width}
    height={height}
  />
  
  <!-- Dark theme logo -->
  <img 
    src="/images/logo-dark.svg" 
    alt="Your Company Logo" 
    class="hidden dark:block transition-opacity duration-200"
    width={width}
    height={height}
  />
</div>

<!-- 
CUSTOMIZATION GUIDE - SEPARATE FILES APPROACH:
==============================================

This component uses two separate SVG files for maximum design flexibility:

FILES TO EDIT:
- /public/images/logo-light.svg (for light theme)
- /public/images/logo-dark.svg (for dark theme)

ADVANTAGES:
✅ Easy for designers to work with
✅ Full control over each theme version
✅ Can use different designs for light/dark
✅ No CSS/Tailwind knowledge required
✅ Works with any design tool (Figma, Illustrator, etc.)

USAGE:
======

Basic usage:
<LogoFiles />

Custom size:
<LogoFiles width="150" height="50" />

With custom classes:
<LogoFiles class="hover:opacity-80" />

DESIGN TIPS:
============

Light Theme Logo (logo-light.svg):
- Use dark colors for text (#111827, #6b7280)
- Use brand colors for icons (#2563eb)
- Ensure good contrast on white/light backgrounds

Dark Theme Logo (logo-dark.svg):
- Use light colors for text (#ffffff, #9ca3af)
- Use lighter brand colors for icons (#60a5fa)
- Ensure good contrast on dark backgrounds

SWITCHING BETWEEN APPROACHES:
=============================

To use this file-based approach instead of the inline SVG:
1. Import LogoFiles instead of Logo in Header.astro
2. Replace <Logo /> with <LogoFiles />

To switch back to inline SVG approach:
1. Import Logo instead of LogoFiles in Header.astro
2. Replace <LogoFiles /> with <Logo />
-->
