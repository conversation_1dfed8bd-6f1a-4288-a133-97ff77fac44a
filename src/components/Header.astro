---
import Navigation from './Navigation.astro';

const currentPath = Astro.url.pathname;
---

<header class="bg-white shadow-sm border-b border-gray-200">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center py-6">
      <!-- Logo -->
      <div class="flex-shrink-0">
        <a href="/" class="text-2xl font-bold text-gray-900 hover:text-gray-700 transition-colors">
          Your Logo
        </a>
      </div>
      
      <!-- Navigation -->
      <Navigation currentPath={currentPath} />
      
      <!-- Mobile menu button (you can enhance this with JavaScript) -->
      <div class="md:hidden">
        <button
          type="button"
          class="text-gray-500 hover:text-gray-700 focus:outline-none focus:text-gray-700"
          aria-label="Toggle menu"
        >
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </div>
    </div>
  </div>
</header>
