---
import Navigation from './Navigation.astro';
import ThemeToggle from './ThemeToggle.astro';
import Logo from './Logo.astro';

const currentPath = Astro.url.pathname;
---

<header class="sticky top-0 z-50 bg-white/80 backdrop-blur-md shadow-sm border-b border-gray-200 dark:bg-gray-900/80 dark:border-gray-700 transition-colors">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center py-4">
      <!-- Logo -->
      <div class="flex-shrink-0">
        <a href="/" class="flex items-center hover:opacity-80 transition-opacity">
          <Logo width="120" height="40" />
        </a>
      </div>

      <!-- Desktop Navigation & Theme Toggle -->
      <div class="hidden md:flex items-center space-x-6">
        <Navigation currentPath={currentPath} />
        <ThemeToggle />
      </div>

      <!-- Mobile menu button & theme toggle -->
      <div class="md:hidden flex items-center space-x-2">
        <ThemeToggle />
        <button
          id="mobile-menu-button"
          type="button"
          class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none focus:text-gray-700 dark:focus:text-gray-300 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          aria-label="Toggle menu"
          onclick="toggleMobileMenu()"
        >
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile Navigation Menu -->
    <div id="mobile-menu" class="md:hidden pb-4" style="display: none;">
      <div class="flex flex-col space-y-2">
        <a href="/" class="px-3 py-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">Home</a>
        <a href="/about" class="px-3 py-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">About</a>
        <a href="/gallery" class="px-3 py-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors pl-6">Gallery</a>
        <a href="/services" class="px-3 py-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">Services</a>
        <a href="/portfolio" class="px-3 py-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">Portfolio</a>
        <a href="/blog" class="px-3 py-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">Blog</a>
        <a href="/contact" class="px-3 py-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">Contact</a>
      </div>
    </div>
  </div>
</header>

<script>
  // Simple mobile menu toggle function
  function toggleMobileMenu() {
    console.log('toggleMobileMenu called');
    const mobileMenu = document.getElementById('mobile-menu');
    const mobileMenuButton = document.getElementById('mobile-menu-button');

    if (mobileMenu) {
      console.log('Mobile menu found, toggling...');
      const isHidden = mobileMenu.style.display === 'none';
      console.log('Current display:', mobileMenu.style.display, 'isHidden:', isHidden);

      if (isHidden) {
        mobileMenu.style.display = 'block';
        console.log('Menu opened - display set to block');
      } else {
        mobileMenu.style.display = 'none';
        console.log('Menu closed - display set to none');
      }

      // Toggle hamburger icon
      const svg = mobileMenuButton?.querySelector('svg');
      if (svg) {
        if (isHidden) {
          // Change to X icon
          svg.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />';
        } else {
          // Change back to hamburger icon
          svg.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />';
        }
      }
    } else {
      console.error('Mobile menu not found');
    }
  }

  // Close mobile menu when clicking on a link
  document.addEventListener('DOMContentLoaded', function() {
    const mobileMenu = document.getElementById('mobile-menu');
    if (mobileMenu) {
      const mobileLinks = mobileMenu.querySelectorAll('a');
      mobileLinks.forEach(link => {
        link.addEventListener('click', function() {
          mobileMenu.style.display = 'none';
          // Reset hamburger icon
          const mobileMenuButton = document.getElementById('mobile-menu-button');
          const svg = mobileMenuButton?.querySelector('svg');
          if (svg) {
            svg.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />';
          }
        });
      });
    }
  });

  // Make function globally available
  (window as any).toggleMobileMenu = toggleMobileMenu;
</script>
