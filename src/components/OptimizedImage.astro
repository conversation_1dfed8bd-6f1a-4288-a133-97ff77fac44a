---
export interface Props {
  src: string;
  alt: string;
  width: number;
  height: number;
  class?: string;
  loading?: 'lazy' | 'eager';
  sizes?: string;
}

const {
  src,
  alt,
  width,
  height,
  class: className = '',
  loading = 'lazy',
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
} = Astro.props;
---

<img
  src={src}
  alt={alt}
  width={width}
  height={height}
  loading={loading}
  sizes={sizes}
  class={`${className} transition-all duration-300`}
  decoding="async"
/>
