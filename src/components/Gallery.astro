---
// Astro-native Gallery component using CSS-only lightbox
// Works with static HTML, no JavaScript required

export interface GalleryImage {
  src: string;
  alt: string;
  title?: string;
  description?: string;
}

export interface Props {
  id: string; // Unique ID for this gallery instance
  title?: string;
  description?: string;
  images: GalleryImage[];
  columns?: number; // Number of columns (2, 3, or 4)
  class?: string;
}

const {
  id,
  title,
  description,
  images,
  columns = 3,
  class: className = ""
} = Astro.props;

const gridCols = {
  2: "grid-cols-1 md:grid-cols-2",
  3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
  4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
}[columns] || "grid-cols-1 md:grid-cols-2 lg:grid-cols-3";
---

<div class={`gallery-container ${className}`}>
  <!-- Gallery Header -->
  {title && (
    <div class="mb-8">
      <h3 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">
        {title}
      </h3>
      {description && (
        <p class="text-lg text-gray-600 dark:text-gray-400 max-w-3xl">
          {description}
        </p>
      )}
    </div>
  )}

  <!-- Gallery Grid with CSS-only lightbox -->
  <div class={`grid ${gridCols} gap-4 md:gap-6`}>
    {images.map((image, index) => {
      const lightboxId = `lightbox-${id}-${index}`;
      return (
        <div class="gallery-item">
          <!-- Thumbnail -->
          <a href={`#${lightboxId}`} class="block group">
            <div class="relative overflow-hidden rounded-lg bg-gray-200 dark:bg-gray-700 aspect-square">
              <img
                src={image.src}
                alt={image.alt}
                class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                loading="lazy"
              />

              <!-- Hover Overlay -->
              <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                  </svg>
                </div>
              </div>

              <!-- Image Title Overlay -->
              {image.title && (
                <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
                  <h4 class="text-white font-medium text-sm">{image.title}</h4>
                </div>
              )}
            </div>
          </a>

          <!-- CSS-only Lightbox -->
          <div id={lightboxId} class="lightbox">
            <div class="lightbox-content">
              <!-- Close button -->
              <a href="#" class="lightbox-close" aria-label="Close lightbox">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </a>

              <!-- Navigation -->
              {index > 0 && (
                <a href={`#lightbox-${id}-${index - 1}`} class="lightbox-nav lightbox-prev" aria-label="Previous image">
                  <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                  </svg>
                </a>
              )}

              {index < images.length - 1 && (
                <a href={`#lightbox-${id}-${index + 1}`} class="lightbox-nav lightbox-next" aria-label="Next image">
                  <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                </a>
              )}

              <!-- Image container -->
              <div class="lightbox-image-container">
                <img
                  src={image.src}
                  alt={image.alt}
                  class="lightbox-image"
                />

                <!-- Image info -->
                {(image.title || image.description) && (
                  <div class="lightbox-info">
                    {image.title && (
                      <h4 class="text-xl font-semibold text-white mb-2">{image.title}</h4>
                    )}
                    {image.description && (
                      <p class="text-gray-300">{image.description}</p>
                    )}
                    <div class="mt-4 text-sm text-gray-400">
                      {index + 1} of {images.length}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      );
    })}
  </div>
</div>

<style>
  /* CSS-only lightbox styles - works perfectly with Astro's static nature */
  .lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
  }

  .lightbox:target {
    opacity: 1;
    visibility: visible;
  }

  .lightbox-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
  }

  .lightbox-close {
    position: absolute;
    top: -3rem;
    right: 0;
    color: white;
    z-index: 10;
    transition: color 0.2s ease;
  }

  .lightbox-close:hover {
    color: #d1d5db;
  }

  .lightbox-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    z-index: 10;
    transition: color 0.2s ease;
    padding: 1rem;
  }

  .lightbox-nav:hover {
    color: #d1d5db;
  }

  .lightbox-prev {
    left: 1rem;
  }

  .lightbox-next {
    right: 1rem;
  }

  .lightbox-image-container {
    background: white;
    border-radius: 0.5rem;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    max-height: 90vh;
  }

  .lightbox-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
    display: block;
  }

  .lightbox-info {
    padding: 1.5rem;
    background: #1f2937;
    color: white;
  }

  /* Gallery item hover effects */
  .gallery-item {
    transition: all 0.3s ease;
  }

  .gallery-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }

  /* Dark mode adjustments */
  @media (prefers-color-scheme: dark) {
    .lightbox-image-container {
      background: #374151;
    }

    .lightbox-info {
      background: #111827;
    }
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .lightbox-content {
      max-width: 95vw;
      max-height: 95vh;
    }

    .lightbox-close {
      top: -2rem;
      right: -0.5rem;
    }

    .lightbox-nav {
      padding: 0.5rem;
    }

    .lightbox-prev {
      left: 0.5rem;
    }

    .lightbox-next {
      right: 0.5rem;
    }

    .lightbox-image {
      max-height: 60vh;
    }

    .lightbox-info {
      padding: 1rem;
    }
  }
</style>
