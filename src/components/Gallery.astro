---
// Astro-native Gallery component using CSS-only lightbox
// Works with static HTML, no JavaScript required

export interface GalleryImage {
  src: string;
  alt: string;
  title?: string;
  description?: string;
}

export interface Props {
  id: string; // Unique ID for this gallery instance
  title?: string;
  description?: string;
  images: GalleryImage[];
  columns?: number; // Number of columns (2, 3, or 4)
  class?: string;
}

const {
  id,
  title,
  description,
  images,
  columns = 3,
  class: className = ""
} = Astro.props;

const gridCols = {
  2: "grid-cols-1 md:grid-cols-2",
  3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
  4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
}[columns] || "grid-cols-1 md:grid-cols-2 lg:grid-cols-3";
---

<div class={`gallery-container ${className}`}>
  <!-- Gallery Header -->
  {title && (
    <div class="mb-8">
      <h3 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">
        {title}
      </h3>
      {description && (
        <p class="text-lg text-gray-600 dark:text-gray-400 max-w-3xl">
          {description}
        </p>
      )}
    </div>
  )}

  <!-- Gallery Grid -->
  <div class={`grid ${gridCols} gap-4 md:gap-6`}>
    {images.map((image, index) => {
      return (
        <div class="gallery-item">
          <!-- Thumbnail -->
          <button
            class="block group w-full text-left"
            onclick={`openLightbox('${id}', ${index})`}
          >
            <div class="relative overflow-hidden rounded-lg bg-gray-200 dark:bg-gray-700 aspect-square">
              <img
                src={image.src}
                alt={image.alt}
                class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                loading="lazy"
              />

              <!-- Hover Overlay -->
              <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                  </svg>
                </div>
              </div>

              <!-- Image Title Overlay -->
              {image.title && (
                <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
                  <h4 class="text-white font-medium text-sm">{image.title}</h4>
                </div>
              )}
            </div>
          </button>
        </div>
      );
    })}
  </div>

  <!-- Simple Lightbox Modal -->
  <div id={`lightbox-${id}`} class="lightbox" style="display: none;">
    <div class="lightbox-content">
      <!-- Close button -->
      <button class="lightbox-close" onclick={`closeLightbox('${id}')`} aria-label="Close lightbox">
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>

      <!-- Navigation -->
      <button class="lightbox-nav lightbox-prev" onclick={`navigateLightbox('${id}', -1)`} aria-label="Previous image">
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
      </button>

      <button class="lightbox-nav lightbox-next" onclick={`navigateLightbox('${id}', 1)`} aria-label="Next image">
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
      </button>

      <!-- Image container -->
      <div class="lightbox-image-container">
        <img
          id={`lightbox-image-${id}`}
          src=""
          alt=""
          class="lightbox-image"
        />

        <!-- Image info -->
        <div id={`lightbox-info-${id}`} class="lightbox-info">
          <h4 id={`lightbox-title-${id}`} class="text-xl font-semibold text-white mb-2"></h4>
          <p id={`lightbox-description-${id}`} class="text-gray-300"></p>
          <div id={`lightbox-counter-${id}`} class="mt-4 text-sm text-gray-400"></div>
        </div>
      </div>
    </div>
  </div>
</div>

<script define:vars={{ id, images }} is:inline>
  // Store gallery data
  window.galleryData = window.galleryData || {};
  window.galleryData[id] = {
    images: images,
    currentIndex: 0
  };

  // Open lightbox
  window.openLightbox = function(galleryId, index) {
    console.log('Opening lightbox for gallery:', galleryId, 'image:', index);
    const data = window.galleryData[galleryId];
    const lightbox = document.getElementById(`lightbox-${galleryId}`);

    if (data && lightbox) {
      data.currentIndex = index;
      updateLightboxContent(galleryId);
      lightbox.style.display = 'flex';
      document.body.style.overflow = 'hidden';
    }
  };

  // Close lightbox
  window.closeLightbox = function(galleryId) {
    const lightbox = document.getElementById(`lightbox-${galleryId}`);
    if (lightbox) {
      lightbox.style.display = 'none';
      document.body.style.overflow = '';
    }
  };

  // Navigate lightbox
  window.navigateLightbox = function(galleryId, direction) {
    const data = window.galleryData[galleryId];
    if (data) {
      data.currentIndex += direction;

      // Wrap around
      if (data.currentIndex < 0) {
        data.currentIndex = data.images.length - 1;
      } else if (data.currentIndex >= data.images.length) {
        data.currentIndex = 0;
      }

      updateLightboxContent(galleryId);
    }
  };

  // Update lightbox content
  function updateLightboxContent(galleryId) {
    const data = window.galleryData[galleryId];
    const image = data.images[data.currentIndex];

    const lightboxImage = document.getElementById(`lightbox-image-${galleryId}`);
    const lightboxTitle = document.getElementById(`lightbox-title-${galleryId}`);
    const lightboxDescription = document.getElementById(`lightbox-description-${galleryId}`);
    const lightboxCounter = document.getElementById(`lightbox-counter-${galleryId}`);

    if (lightboxImage) {
      lightboxImage.src = image.src;
      lightboxImage.alt = image.alt;
    }

    if (lightboxTitle) {
      lightboxTitle.textContent = image.title || '';
      lightboxTitle.style.display = image.title ? 'block' : 'none';
    }

    if (lightboxDescription) {
      lightboxDescription.textContent = image.description || '';
      lightboxDescription.style.display = image.description ? 'block' : 'none';
    }

    if (lightboxCounter) {
      lightboxCounter.textContent = `${data.currentIndex + 1} of ${data.images.length}`;
    }
  }

  // Keyboard navigation
  document.addEventListener('keydown', function(e) {
    const openLightbox = document.querySelector('.lightbox[style*="flex"]');
    if (openLightbox) {
      const galleryId = openLightbox.id.replace('lightbox-', '');

      if (e.key === 'Escape') {
        window.closeLightbox(galleryId);
      } else if (e.key === 'ArrowLeft') {
        window.navigateLightbox(galleryId, -1);
      } else if (e.key === 'ArrowRight') {
        window.navigateLightbox(galleryId, 1);
      }
    }
  });
</script>

<style>
  /* Simple lightbox styles */
  .lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 9999;
    align-items: center;
    justify-content: center;
    padding: 1rem;
  }

  .lightbox-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
  }

  .lightbox-close {
    position: absolute;
    top: -3rem;
    right: 0;
    color: white;
    z-index: 10;
    transition: color 0.2s ease;
    background: none;
    border: none;
    cursor: pointer;
  }

  .lightbox-close:hover {
    color: #d1d5db;
  }

  .lightbox-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    z-index: 10;
    transition: color 0.2s ease;
    padding: 1rem;
    background: none;
    border: none;
    cursor: pointer;
  }

  .lightbox-nav:hover {
    color: #d1d5db;
  }

  .lightbox-prev {
    left: 1rem;
  }

  .lightbox-next {
    right: 1rem;
  }

  .lightbox-image-container {
    background: white;
    border-radius: 0.5rem;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    max-height: 90vh;
  }

  .lightbox-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
    display: block;
  }

  .lightbox-info {
    padding: 1.5rem;
    background: #1f2937;
    color: white;
  }

  /* Gallery item hover effects */
  .gallery-item {
    transition: all 0.3s ease;
  }

  .gallery-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }

  /* Dark mode adjustments */
  .dark .lightbox-image-container {
    background: #374151;
  }

  .dark .lightbox-info {
    background: #111827;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .lightbox-content {
      max-width: 95vw;
      max-height: 95vh;
    }

    .lightbox-close {
      top: -2rem;
      right: -0.5rem;
    }

    .lightbox-nav {
      padding: 0.5rem;
    }

    .lightbox-prev {
      left: 0.5rem;
    }

    .lightbox-next {
      right: 0.5rem;
    }

    .lightbox-image {
      max-height: 60vh;
    }

    .lightbox-info {
      padding: 1rem;
    }
  }
</style>
</style>
