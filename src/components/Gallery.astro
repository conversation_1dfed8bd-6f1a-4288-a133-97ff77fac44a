---
// Smart Gallery component with modal functionality
// Supports multiple galleries on the same page

export interface GalleryImage {
  src: string;
  alt: string;
  title?: string;
  description?: string;
}

export interface Props {
  id: string; // Unique ID for this gallery instance
  title?: string;
  description?: string;
  images: GalleryImage[];
  columns?: number; // Number of columns (2, 3, or 4)
  class?: string;
}

const { 
  id, 
  title, 
  description, 
  images, 
  columns = 3, 
  class: className = "" 
} = Astro.props;

// Generate unique IDs for this gallery instance
const modalId = `gallery-modal-${id}`;
const gridCols = {
  2: "grid-cols-1 md:grid-cols-2",
  3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3", 
  4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
}[columns] || "grid-cols-1 md:grid-cols-2 lg:grid-cols-3";
---

<div class={`gallery-container ${className}`}>
  <!-- Gallery Header -->
  {title && (
    <div class="mb-8">
      <h3 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">
        {title}
      </h3>
      {description && (
        <p class="text-lg text-gray-600 dark:text-gray-400 max-w-3xl">
          {description}
        </p>
      )}
    </div>
  )}

  <!-- Gallery Grid -->
  <div class={`grid ${gridCols} gap-4 md:gap-6`}>
    {images.map((image, index) => (
      <div class="gallery-item group cursor-pointer">
        <div class="relative overflow-hidden rounded-lg bg-gray-200 dark:bg-gray-700 aspect-square">
          <img
            src={image.src}
            alt={image.alt}
            class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            loading="lazy"
            onclick={`openGalleryModal('${modalId}', ${index})`}
          />
          
          <!-- Hover Overlay -->
          <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
            <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
              </svg>
            </div>
          </div>
          
          <!-- Image Title Overlay -->
          {image.title && (
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
              <h4 class="text-white font-medium text-sm">{image.title}</h4>
            </div>
          )}
        </div>
      </div>
    ))}
  </div>

  <!-- Modal -->
  <div 
    id={modalId}
    class="fixed inset-0 z-50 hidden bg-black bg-opacity-90 flex items-center justify-center p-4"
    onclick={`closeGalleryModal('${modalId}')`}
  >
    <div class="relative max-w-4xl max-h-full">
      <!-- Close Button -->
      <button 
        class="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors z-10"
        onclick={`closeGalleryModal('${modalId}')`}
        aria-label="Close modal"
      >
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>

      <!-- Navigation Arrows -->
      <button 
        class="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 transition-colors z-10"
        onclick={`navigateGallery('${modalId}', -1)`}
        aria-label="Previous image"
        id={`${modalId}-prev`}
      >
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
      </button>
      
      <button 
        class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 transition-colors z-10"
        onclick={`navigateGallery('${modalId}', 1)`}
        aria-label="Next image"
        id={`${modalId}-next`}
      >
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
      </button>

      <!-- Modal Content -->
      <div class="bg-white dark:bg-gray-800 rounded-lg overflow-hidden max-h-full flex flex-col" onclick="event.stopPropagation()">
        <!-- Modal Image -->
        <div class="flex-1 flex items-center justify-center p-4 bg-gray-100 dark:bg-gray-900">
          <img
            id={`${modalId}-image`}
            src=""
            alt=""
            class="max-w-full max-h-[70vh] object-contain"
          />
        </div>
        
        <!-- Modal Info -->
        <div class="p-6 bg-white dark:bg-gray-800" id={`${modalId}-info`}>
          <h4 class="text-xl font-semibold text-gray-900 dark:text-white mb-2" id={`${modalId}-title`}></h4>
          <p class="text-gray-600 dark:text-gray-400" id={`${modalId}-description`}></p>
          <div class="mt-4 flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
            <span id={`${modalId}-counter`}></span>
            <span>Press ESC to close</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script define:vars={{ modalId, images }}>
  // Gallery data for this instance
  window.galleryData = window.galleryData || {};
  window.galleryData[modalId] = {
    images: images,
    currentIndex: 0
  };

  // Open modal function
  window.openGalleryModal = function(modalId, index) {
    const modal = document.getElementById(modalId);
    const data = window.galleryData[modalId];
    
    if (modal && data) {
      data.currentIndex = index;
      updateModalContent(modalId);
      modal.classList.remove('hidden');
      document.body.style.overflow = 'hidden';
    }
  };

  // Close modal function
  window.closeGalleryModal = function(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
      modal.classList.add('hidden');
      document.body.style.overflow = '';
    }
  };

  // Navigate gallery function
  window.navigateGallery = function(modalId, direction) {
    const data = window.galleryData[modalId];
    if (data) {
      data.currentIndex += direction;
      
      // Wrap around
      if (data.currentIndex < 0) {
        data.currentIndex = data.images.length - 1;
      } else if (data.currentIndex >= data.images.length) {
        data.currentIndex = 0;
      }
      
      updateModalContent(modalId);
    }
  };

  // Update modal content
  function updateModalContent(modalId) {
    const data = window.galleryData[modalId];
    const image = data.images[data.currentIndex];
    
    const modalImage = document.getElementById(`${modalId}-image`);
    const modalTitle = document.getElementById(`${modalId}-title`);
    const modalDescription = document.getElementById(`${modalId}-description`);
    const modalCounter = document.getElementById(`${modalId}-counter`);
    const modalInfo = document.getElementById(`${modalId}-info`);
    
    if (modalImage) {
      modalImage.src = image.src;
      modalImage.alt = image.alt;
    }
    
    if (modalTitle) {
      modalTitle.textContent = image.title || '';
      modalTitle.style.display = image.title ? 'block' : 'none';
    }
    
    if (modalDescription) {
      modalDescription.textContent = image.description || '';
      modalDescription.style.display = image.description ? 'block' : 'none';
    }
    
    if (modalCounter) {
      modalCounter.textContent = `${data.currentIndex + 1} of ${data.images.length}`;
    }
    
    // Hide info section if no title or description
    if (modalInfo && !image.title && !image.description) {
      modalInfo.style.display = 'none';
    } else if (modalInfo) {
      modalInfo.style.display = 'block';
    }
  }

  // Keyboard navigation
  document.addEventListener('keydown', function(e) {
    const openModal = document.querySelector('.fixed.inset-0:not(.hidden)');
    if (openModal) {
      const modalId = openModal.id;
      
      if (e.key === 'Escape') {
        window.closeGalleryModal(modalId);
      } else if (e.key === 'ArrowLeft') {
        window.navigateGallery(modalId, -1);
      } else if (e.key === 'ArrowRight') {
        window.navigateGallery(modalId, 1);
      }
    }
  });
</script>

<style>
  .gallery-item {
    @apply transition-all duration-300;
  }
  
  .gallery-item:hover {
    @apply transform -translate-y-1 shadow-lg;
  }
</style>
