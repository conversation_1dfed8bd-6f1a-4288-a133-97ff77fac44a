---
export interface Props {
  title?: string;
  description?: string;
  image?: string;
  canonical?: string;
  noindex?: boolean;
  type?: 'website' | 'article';
}

const {
  title = "Your Site Title",
  description = "Your site description goes here",
  image = "/og-image.jpg",
  canonical,
  noindex = false,
  type = 'website',
} = Astro.props;

const canonicalURL = canonical || new URL(Astro.url.pathname, Astro.site);
const socialImageURL = new URL(image, Astro.url).href;
---

<!-- Primary Meta Tags -->
<title>{title}</title>
<meta name="title" content={title} />
<meta name="description" content={description} />

<!-- Canonical URL -->
<link rel="canonical" href={canonicalURL} />

<!-- Open Graph / Facebook -->
<meta property="og:type" content={type} />
<meta property="og:url" content={Astro.url} />
<meta property="og:title" content={title} />
<meta property="og:description" content={description} />
<meta property="og:image" content={socialImageURL} />

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image" />
<meta property="twitter:url" content={Astro.url} />
<meta property="twitter:title" content={title} />
<meta property="twitter:description" content={description} />
<meta property="twitter:image" content={socialImageURL} />

<!-- Robots -->
{noindex && <meta name="robots" content="noindex" />}
