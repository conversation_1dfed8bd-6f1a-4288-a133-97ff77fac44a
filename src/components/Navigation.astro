---
export interface Props {
  currentPath: string;
}

const { currentPath } = Astro.props;

const navItems = [
  { name: 'Home', href: '/' },
  { name: 'About', href: '/about' },
  { name: 'Services', href: '/services' },
  { name: 'Portfolio', href: '/portfolio' },
  { name: 'Blog', href: '/blog' },
  { name: 'Contact', href: '/contact' },
];

function isActive(href: string, currentPath: string): boolean {
  if (href === '/') {
    return currentPath === '/';
  }
  return currentPath.startsWith(href);
}
---

<nav class="flex space-x-1">
  {navItems.map((item) => (
    <a
      href={item.href}
      class={`px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
        isActive(item.href, currentPath)
          ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 shadow-sm'
          : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800'
      }`}
    >
      {item.name}
    </a>
  ))}
</nav>
