---
export interface Props {
  currentPath: string;
}

const { currentPath } = Astro.props;

const navItems = [
  { name: 'Home', href: '/' },
  { name: 'About', href: '/about' },
  { name: 'Services', href: '/services' },
  { name: 'Portfolio', href: '/portfolio' },
  { name: 'Blog', href: '/blog' },
  { name: 'Contact', href: '/contact' },
];

function isActive(href: string, currentPath: string): boolean {
  if (href === '/') {
    return currentPath === '/';
  }
  return currentPath.startsWith(href);
}
---

<nav class="hidden md:flex space-x-8">
  {navItems.map((item) => (
    <a
      href={item.href}
      class={`text-sm font-medium transition-colors ${
        isActive(item.href, currentPath)
          ? 'text-blue-600 border-b-2 border-blue-600 pb-1'
          : 'text-gray-700 hover:text-blue-600'
      }`}
    >
      {item.name}
    </a>
  ))}
</nav>
