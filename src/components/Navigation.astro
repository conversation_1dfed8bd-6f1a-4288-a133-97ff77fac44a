---
export interface Props {
  currentPath: string;
}

const { currentPath } = Astro.props;

const navItems = [
  { name: 'Home', href: '/' },
  {
    name: 'About',
    href: '/about',
    dropdown: [
      { name: 'Our Story', href: '/about' },
      { name: 'Gallery', href: '/gallery' }
    ]
  },
  { name: 'Services', href: '/services' },
  { name: 'Portfolio', href: '/portfolio' },
  { name: 'Blog', href: '/blog' },
  { name: 'Contact', href: '/contact' },
];

function isActive(href: string, currentPath: string): boolean {
  if (href === '/') {
    return currentPath === '/';
  }
  return currentPath.startsWith(href);
}

function isDropdownActive(item: any, currentPath: string): boolean {
  if (item.dropdown) {
    return item.dropdown.some((subItem: any) => isActive(subItem.href, currentPath));
  }
  return isActive(item.href, currentPath);
}
---

<nav class="flex space-x-1">
  {navItems.map((item) => (
    item.dropdown ? (
      <!-- Dropdown Navigation Item -->
      <div class="relative group">
        <button
          class={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
            isDropdownActive(item, currentPath)
              ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 shadow-sm'
              : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800'
          }`}
        >
          {item.name}
          <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </button>

        <!-- Dropdown Menu -->
        <div class="absolute left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
          <div class="py-1">
            {item.dropdown.map((subItem: any) => (
              <a
                href={subItem.href}
                class={`block px-4 py-2 text-sm transition-colors ${
                  isActive(subItem.href, currentPath)
                    ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20'
                    : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                {subItem.name}
              </a>
            ))}
          </div>
        </div>
      </div>
    ) : (
      <!-- Regular Navigation Item -->
      <a
        href={item.href}
        class={`px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
          isActive(item.href, currentPath)
            ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 shadow-sm'
            : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800'
        }`}
      >
        {item.name}
      </a>
    )
  ))}
</nav>
