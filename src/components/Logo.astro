---
// Logo component with dark/light theme support
// Easy to customize - just update the SVG paths and colors below

export interface Props {
  class?: string;
  width?: string;
  height?: string;
}

const { class: className = "", width = "120", height = "40" } = Astro.props;
---

<div class={`inline-flex items-center ${className}`}>
  <!-- SVG Logo with theme-aware styling -->
  <svg 
    width={width} 
    height={height} 
    viewBox="0 0 120 40" 
    fill="none" 
    xmlns="http://www.w3.org/2000/svg"
    class="transition-colors duration-200"
  >
    <!-- Logo Icon/Symbol -->
    <g class="logo-icon">
      <!-- Main circle/shape -->
      <circle 
        cx="20" 
        cy="20" 
        r="16" 
        class="fill-blue-600 dark:fill-blue-400 transition-colors"
      />
      
      <!-- Inner design element -->
      <path 
        d="M12 20L18 26L28 14" 
        stroke="white" 
        stroke-width="2.5" 
        stroke-linecap="round" 
        stroke-linejoin="round"
        class="stroke-white dark:stroke-gray-900"
      />
      
      <!-- Optional: Additional design elements -->
      <circle 
        cx="20" 
        cy="20" 
        r="16" 
        stroke="currentColor" 
        stroke-width="1" 
        fill="none"
        class="text-blue-600 dark:text-blue-400 opacity-30"
      />
    </g>
    
    <!-- Company Name Text -->
    <g class="logo-text">
      <!-- Main company name -->
      <text 
        x="45" 
        y="16" 
        class="fill-gray-900 dark:fill-white transition-colors text-lg font-bold"
        font-family="system-ui, -apple-system, sans-serif"
        font-size="16"
        font-weight="700"
      >
        Your Company
      </text>
      
      <!-- Optional tagline -->
      <text 
        x="45" 
        y="28" 
        class="fill-gray-600 dark:fill-gray-400 transition-colors text-xs"
        font-family="system-ui, -apple-system, sans-serif"
        font-size="10"
        font-weight="400"
      >
        Digital Solutions
      </text>
    </g>
  </svg>
</div>

<!-- 
CUSTOMIZATION GUIDE:
====================

1. COLORS:
   - Primary brand color: Change "blue-600/blue-400" classes
   - Text colors: Modify "gray-900/white" and "gray-600/gray-400"
   - Icon stroke: Update stroke colors in the path elements

2. COMPANY NAME:
   - Update the text content in the <text> elements
   - Adjust x/y positions if needed for alignment
   - Modify font-size for different text sizes

3. ICON DESIGN:
   - Replace the circle and path elements with your own design
   - Keep the theme-aware classes for proper dark mode support
   - Maintain the same structure for consistency

4. SIZE:
   - Default: 120x40 viewBox
   - Adjust viewBox and element positions proportionally
   - Use width/height props to scale the component

5. ALTERNATIVE APPROACH - Using separate SVG files:
   If you prefer separate light/dark SVG files, replace the entire
   SVG content with:
   
   <img 
     src="/images/logo-light.svg" 
     alt="Company Logo"
     class="block dark:hidden transition-opacity"
     width={width}
     height={height}
   />
   <img 
     src="/images/logo-dark.svg" 
     alt="Company Logo" 
     class="hidden dark:block transition-opacity"
     width={width}
     height={height}
   />

USAGE EXAMPLES:
===============

Basic usage:
<Logo />

Custom size:
<Logo width="150" height="50" />

With custom classes:
<Logo class="hover:opacity-80" />

In navigation:
<Logo width="100" height="32" class="mr-4" />
-->
