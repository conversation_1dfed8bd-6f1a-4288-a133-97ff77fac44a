---
export interface Props {
  title?: string;
  description?: string;
  image?: string;
  canonical?: string;
  noindex?: boolean;
}

const {
  title = "Your Site Title",
  description = "Your site description goes here",
  image = "/og-image.jpg",
  canonical,
  noindex = false,
} = Astro.props;

const canonicalURL = canonical || new URL(Astro.url.pathname, Astro.site);
const socialImageURL = new URL(image, Astro.url).href;
---

<!doctype html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description} />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />

    <!-- Canonical URL -->
    <link rel="canonical" href={canonicalURL} />

    <!-- Primary Meta Tags -->
    <title>{title}</title>
    <meta name="title" content={title} />
    <meta name="description" content={description} />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content={Astro.url} />
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:image" content={socialImageURL} />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content={Astro.url} />
    <meta property="twitter:title" content={title} />
    <meta property="twitter:description" content={description} />
    <meta property="twitter:image" content={socialImageURL} />

    <!-- Robots -->
    {noindex && <meta name="robots" content="noindex" />}

    <!-- Theme color for mobile browsers -->
    <meta name="theme-color" content="#ffffff" media="(prefers-color-scheme: light)" />
    <meta name="theme-color" content="#111827" media="(prefers-color-scheme: dark)" />

    <!-- Preload fonts (add your custom fonts here) -->
    <!-- <link rel="preload" href="/fonts/your-font.woff2" as="font" type="font/woff2" crossorigin> -->

    <!-- Custom CSS -->
    <link rel="stylesheet" href="/styles/main.css" />

    <!-- Theme initialization script (prevents flash) -->
    <script is:inline>
      // Prevent flash of unstyled content
      (function() {
        const theme = localStorage.getItem('theme') ||
          (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');

        if (theme === 'dark') {
          document.documentElement.classList.add('dark');
        }
      })();
    </script>
  </head>
  <body class="min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 antialiased transition-colors">
    <div class="flex min-h-screen flex-col">
      <slot />
    </div>

    <!-- Custom JavaScript -->
    <script src="/scripts/main.js" is:inline></script>
  </body>
</html>
