---
import BaseLayout from './BaseLayout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';

export interface Props {
  title?: string;
  description?: string;
  image?: string;
  canonical?: string;
  noindex?: boolean;
}

const props = Astro.props;
---

<BaseLayout {...props}>
  <Header />
  <main class="flex-1">
    <slot />
  </main>
  <Footer />
</BaseLayout>
