---
import MainLayout from '../layouts/MainLayout.astro';
import Button from '../components/Button.astro';
---

<MainLayout 
  title="Portfolio - Your Site Title"
  description="Explore our portfolio of successful projects and see how we've helped businesses achieve their goals."
>
  <!-- <PERSON> Header -->
  <section class="bg-gray-50 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
          Our Portfolio
        </h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Take a look at some of our recent projects and see how we've helped businesses like yours succeed.
        </p>
      </div>
    </div>
  </section>

  <!-- Portfolio Grid -->
  <section class="py-24 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Project 1 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-lg transition-shadow">
          <div class="h-48 overflow-hidden">
            <img
              src="/images/placeholder-800x600.svg"
              alt="E-commerce Platform Project"
              class="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
              width="800"
              height="600"
            />
          </div>
          <div class="p-6">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-blue-600 font-medium">Web Development</span>
              <span class="text-sm text-gray-500">2024</span>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">E-commerce Platform</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
              A modern e-commerce platform built with cutting-edge technology, featuring seamless user experience and robust backend.
            </p>
            <div class="flex flex-wrap gap-2 mb-4">
              <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded">React</span>
              <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded">Node.js</span>
              <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded">MongoDB</span>
            </div>
            <Button href="#" variant="outline" size="sm">
              View Project
            </Button>
          </div>
        </div>

        <!-- Project 2 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-lg transition-shadow">
          <div class="h-48 overflow-hidden">
            <img
              src="/images/placeholder-800x600.svg"
              alt="Fitness Tracking App Project"
              class="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
              width="800"
              height="600"
            />
          </div>
          <div class="p-6">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-blue-600 font-medium">Mobile App</span>
              <span class="text-sm text-gray-500">2024</span>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Fitness Tracking App</h3>
            <p class="text-gray-600 mb-4">
              A comprehensive fitness tracking mobile application with social features and personalized workout plans.
            </p>
            <div class="flex flex-wrap gap-2 mb-4">
              <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">React Native</span>
              <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">Firebase</span>
              <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">Redux</span>
            </div>
            <Button href="#" variant="outline" size="sm">
              View Project
            </Button>
          </div>
        </div>

        <!-- Project 3 -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-lg transition-shadow">
          <div class="h-48 overflow-hidden">
            <img
              src="/images/placeholder-800x600.svg"
              alt="Tech Startup Branding Project"
              class="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
              width="800"
              height="600"
            />
          </div>
          <div class="p-6">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-blue-600 font-medium">Brand Design</span>
              <span class="text-sm text-gray-500">2023</span>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Tech Startup Branding</h3>
            <p class="text-gray-600 mb-4">
              Complete brand identity design for a tech startup, including logo, color palette, and marketing materials.
            </p>
            <div class="flex flex-wrap gap-2 mb-4">
              <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">Logo Design</span>
              <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">Brand Guidelines</span>
              <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">Print Design</span>
            </div>
            <Button href="#" variant="outline" size="sm">
              View Project
            </Button>
          </div>
        </div>

        <!-- Project 4 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow">
          <div class="bg-gray-200 h-48 flex items-center justify-center">
            <p class="text-gray-500">Project Image</p>
          </div>
          <div class="p-6">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-blue-600 font-medium">Web Development</span>
              <span class="text-sm text-gray-500">2023</span>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Corporate Website</h3>
            <p class="text-gray-600 mb-4">
              Professional corporate website with content management system and multi-language support.
            </p>
            <div class="flex flex-wrap gap-2 mb-4">
              <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">WordPress</span>
              <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">PHP</span>
              <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">MySQL</span>
            </div>
            <Button href="#" variant="outline" size="sm">
              View Project
            </Button>
          </div>
        </div>

        <!-- Project 5 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow">
          <div class="bg-gray-200 h-48 flex items-center justify-center">
            <p class="text-gray-500">Project Image</p>
          </div>
          <div class="p-6">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-blue-600 font-medium">Digital Marketing</span>
              <span class="text-sm text-gray-500">2023</span>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">SEO Campaign</h3>
            <p class="text-gray-600 mb-4">
              Comprehensive SEO campaign that increased organic traffic by 300% and improved search rankings.
            </p>
            <div class="flex flex-wrap gap-2 mb-4">
              <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">SEO</span>
              <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">Content Strategy</span>
              <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">Analytics</span>
            </div>
            <Button href="#" variant="outline" size="sm">
              View Project
            </Button>
          </div>
        </div>

        <!-- Project 6 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow">
          <div class="bg-gray-200 h-48 flex items-center justify-center">
            <p class="text-gray-500">Project Image</p>
          </div>
          <div class="p-6">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm text-blue-600 font-medium">Web App</span>
              <span class="text-sm text-gray-500">2023</span>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Project Management Tool</h3>
            <p class="text-gray-600 mb-4">
              Custom project management web application with real-time collaboration and reporting features.
            </p>
            <div class="flex flex-wrap gap-2 mb-4">
              <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">Vue.js</span>
              <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">Laravel</span>
              <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">PostgreSQL</span>
            </div>
            <Button href="#" variant="outline" size="sm">
              View Project
            </Button>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="py-24 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
        Ready to Start Your Project?
      </h2>
      <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
        Let's work together to create something amazing for your business.
      </p>
      <Button href="/contact" size="lg">
        Get Started Today
      </Button>
    </div>
  </section>
</MainLayout>
