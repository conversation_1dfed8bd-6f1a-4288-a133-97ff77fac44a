---
import MainLayout from '../layouts/MainLayout.astro';
import Button from '../components/Button.astro';
---

<MainLayout 
  title="Services - Your Site Title"
  description="Discover our comprehensive range of services designed to help your business succeed."
>
  <!-- Page Header -->
  <section class="bg-gray-50 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
          Our Services
        </h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          We offer a comprehensive range of services to help your business grow and succeed in today's competitive market.
        </p>
      </div>
    </div>
  </section>

  <!-- Services Grid -->
  <section class="py-24 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Service 1 -->
        <div class="bg-white border border-gray-200 rounded-lg p-8 hover:shadow-lg transition-shadow">
          <div class="bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mb-6">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-4">Web Development</h3>
          <p class="text-gray-600 mb-6">
            Custom websites and web applications built with modern technologies and best practices.
          </p>
          <ul class="text-sm text-gray-600 space-y-2 mb-6">
            <li>• Responsive design</li>
            <li>• Modern frameworks</li>
            <li>• SEO optimization</li>
            <li>• Performance focused</li>
          </ul>
          <Button href="/contact" variant="outline" size="sm">
            Learn More
          </Button>
        </div>

        <!-- Service 2 -->
        <div class="bg-white border border-gray-200 rounded-lg p-8 hover:shadow-lg transition-shadow">
          <div class="bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mb-6">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-4">Mobile Apps</h3>
          <p class="text-gray-600 mb-6">
            Native and cross-platform mobile applications for iOS and Android devices.
          </p>
          <ul class="text-sm text-gray-600 space-y-2 mb-6">
            <li>• iOS & Android</li>
            <li>• Cross-platform solutions</li>
            <li>• App Store optimization</li>
            <li>• User-friendly design</li>
          </ul>
          <Button href="/contact" variant="outline" size="sm">
            Learn More
          </Button>
        </div>

        <!-- Service 3 -->
        <div class="bg-white border border-gray-200 rounded-lg p-8 hover:shadow-lg transition-shadow">
          <div class="bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mb-6">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-4">Digital Marketing</h3>
          <p class="text-gray-600 mb-6">
            Comprehensive digital marketing strategies to grow your online presence.
          </p>
          <ul class="text-sm text-gray-600 space-y-2 mb-6">
            <li>• SEO & SEM</li>
            <li>• Social media marketing</li>
            <li>• Content strategy</li>
            <li>• Analytics & reporting</li>
          </ul>
          <Button href="/contact" variant="outline" size="sm">
            Learn More
          </Button>
        </div>

        <!-- Service 4 -->
        <div class="bg-white border border-gray-200 rounded-lg p-8 hover:shadow-lg transition-shadow">
          <div class="bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mb-6">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-4">Brand Design</h3>
          <p class="text-gray-600 mb-6">
            Creative brand identity and design solutions that make your business stand out.
          </p>
          <ul class="text-sm text-gray-600 space-y-2 mb-6">
            <li>• Logo design</li>
            <li>• Brand guidelines</li>
            <li>• Marketing materials</li>
            <li>• Visual identity</li>
          </ul>
          <Button href="/contact" variant="outline" size="sm">
            Learn More
          </Button>
        </div>

        <!-- Service 5 -->
        <div class="bg-white border border-gray-200 rounded-lg p-8 hover:shadow-lg transition-shadow">
          <div class="bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mb-6">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-4">Consulting</h3>
          <p class="text-gray-600 mb-6">
            Strategic consulting to help you make informed decisions and optimize your business.
          </p>
          <ul class="text-sm text-gray-600 space-y-2 mb-6">
            <li>• Business strategy</li>
            <li>• Technology consulting</li>
            <li>• Process optimization</li>
            <li>• Growth planning</li>
          </ul>
          <Button href="/contact" variant="outline" size="sm">
            Learn More
          </Button>
        </div>

        <!-- Service 6 -->
        <div class="bg-white border border-gray-200 rounded-lg p-8 hover:shadow-lg transition-shadow">
          <div class="bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mb-6">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-4">Support & Maintenance</h3>
          <p class="text-gray-600 mb-6">
            Ongoing support and maintenance to keep your systems running smoothly.
          </p>
          <ul class="text-sm text-gray-600 space-y-2 mb-6">
            <li>• 24/7 monitoring</li>
            <li>• Regular updates</li>
            <li>• Bug fixes</li>
            <li>• Performance optimization</li>
          </ul>
          <Button href="/contact" variant="outline" size="sm">
            Learn More
          </Button>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="py-24 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
        Ready to Get Started?
      </h2>
      <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
        Let's discuss your project and see how our services can help you achieve your goals.
      </p>
      <Button href="/contact" size="lg">
        Contact Us Today
      </Button>
    </div>
  </section>
</MainLayout>
