---
import MainLayout from '../layouts/MainLayout.astro';
import Gallery from '../components/Gallery.astro';

// Sample gallery data - replace with your actual images
const officeGallery = [
  {
    src: "/images/placeholder-800x600.svg",
    alt: "Modern office workspace",
    title: "Open Workspace",
    description: "Our collaborative open workspace designed for creativity and productivity."
  },
  {
    src: "/images/placeholder-800x600.svg", 
    alt: "Conference room",
    title: "Conference Room",
    description: "State-of-the-art conference room equipped with the latest technology."
  },
  {
    src: "/images/placeholder-800x600.svg",
    alt: "Break area",
    title: "Break Area", 
    description: "Comfortable break area where our team relaxes and connects."
  },
  {
    src: "/images/placeholder-800x600.svg",
    alt: "Reception area",
    title: "Reception",
    description: "Welcoming reception area that reflects our company culture."
  },
  {
    src: "/images/placeholder-800x600.svg",
    alt: "Creative studio",
    title: "Creative Studio",
    description: "Dedicated space for brainstorming and creative collaboration."
  },
  {
    src: "/images/placeholder-800x600.svg",
    alt: "Outdoor terrace",
    title: "Outdoor Terrace",
    description: "Beautiful outdoor space for meetings and team events."
  }
];

const teamGallery = [
  {
    src: "/images/placeholder-800x600.svg",
    alt: "Team meeting",
    title: "Team Collaboration",
    description: "Our team working together on exciting new projects."
  },
  {
    src: "/images/placeholder-800x600.svg",
    alt: "Company event",
    title: "Annual Company Event",
    description: "Celebrating our achievements and milestones together."
  },
  {
    src: "/images/placeholder-800x600.svg",
    alt: "Workshop session",
    title: "Learning Workshop",
    description: "Continuous learning and skill development sessions."
  },
  {
    src: "/images/placeholder-800x600.svg",
    alt: "Team lunch",
    title: "Team Lunch",
    description: "Building relationships over shared meals and conversations."
  }
];

const projectGallery = [
  {
    src: "/images/placeholder-800x600.svg",
    alt: "Project showcase",
    title: "E-commerce Platform",
    description: "Screenshots from our award-winning e-commerce platform project."
  },
  {
    src: "/images/placeholder-800x600.svg",
    alt: "Mobile app design",
    title: "Mobile App Interface",
    description: "User interface design for our latest mobile application."
  },
  {
    src: "/images/placeholder-800x600.svg",
    alt: "Website design",
    title: "Corporate Website",
    description: "Modern website design showcasing clean aesthetics and functionality."
  },
  {
    src: "/images/placeholder-800x600.svg",
    alt: "Dashboard design",
    title: "Analytics Dashboard",
    description: "Data visualization dashboard with intuitive user experience."
  },
  {
    src: "/images/placeholder-800x600.svg",
    alt: "Branding project",
    title: "Brand Identity",
    description: "Complete brand identity package including logo and guidelines."
  },
  {
    src: "/images/placeholder-800x600.svg",
    alt: "Marketing materials",
    title: "Marketing Campaign",
    description: "Creative marketing materials for digital and print campaigns."
  },
  {
    src: "/images/placeholder-800x600.svg",
    alt: "Product design",
    title: "Product Design",
    description: "Innovative product design combining form and function."
  },
  {
    src: "/images/placeholder-800x600.svg",
    alt: "User research",
    title: "User Research",
    description: "Behind-the-scenes look at our user research and testing process."
  }
];
---

<MainLayout 
  title="Gallery - Your Site Title"
  description="Explore our office spaces, team culture, and project showcases through our comprehensive photo gallery."
>
  <!-- Page Header -->
  <section class="bg-gray-50 dark:bg-gray-800 py-16 transition-colors">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
          Gallery
        </h1>
        <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          Take a visual journey through our workspace, team culture, and creative projects. 
          Get an inside look at what makes our company special.
        </p>
      </div>
    </div>
  </section>

  <!-- Office Spaces Gallery -->
  <section class="py-24 bg-white dark:bg-gray-900 transition-colors">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <Gallery 
        id="office-spaces"
        title="Our Office Spaces"
        description="Step inside our modern, thoughtfully designed workspace that fosters creativity, collaboration, and innovation. From open work areas to quiet focus zones, every space is crafted to support our team's best work."
        images={officeGallery}
        columns={3}
      />
    </div>
  </section>

  <!-- Team Culture Gallery -->
  <section class="py-24 bg-gray-50 dark:bg-gray-800 transition-colors">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <Gallery 
        id="team-culture"
        title="Team & Culture"
        description="Meet the amazing people behind our success. Our team culture is built on collaboration, continuous learning, and celebrating achievements together. These moments capture the spirit of our workplace."
        images={teamGallery}
        columns={2}
      />
    </div>
  </section>

  <!-- Projects Showcase Gallery -->
  <section class="py-24 bg-white dark:bg-gray-900 transition-colors">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <Gallery 
        id="project-showcase"
        title="Project Showcase"
        description="Explore our portfolio of successful projects across web development, mobile apps, branding, and digital marketing. Each project represents our commitment to quality, innovation, and client satisfaction."
        images={projectGallery}
        columns={4}
      />
    </div>
  </section>

  <!-- Call to Action -->
  <section class="py-24 bg-gray-50 dark:bg-gray-800 transition-colors">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
        Want to Visit Our Office?
      </h2>
      <p class="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
        We'd love to show you around and discuss how we can help bring your project to life. 
        Schedule a visit or virtual tour today.
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a 
          href="/contact" 
          class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 transition-colors"
        >
          Schedule a Visit
        </a>
        <a 
          href="/about" 
          class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-base font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          Learn More About Us
        </a>
      </div>
    </div>
  </section>
</MainLayout>
