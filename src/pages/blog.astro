---
import MainLayout from '../layouts/MainLayout.astro';
import Button from '../components/Button.astro';
// Note: Replace these with actual JPG imports when you add real images
// import { Image } from 'astro:assets';
// import blog1Img from '../assets/images/blog/blog-1.jpg';
---

<MainLayout 
  title="Blog - Your Site Title"
  description="Read our latest insights, tips, and updates about web development, design, and digital marketing."
>
  <!-- Page Header -->
  <section class="bg-gray-50 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
          Our Blog
        </h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Stay updated with the latest insights, tips, and trends in web development, design, and digital marketing.
        </p>
      </div>
    </div>
  </section>

  <!-- Blog Posts -->
  <section class="py-24 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Blog Post 1 -->
        <article class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-lg transition-shadow">
          <div class="h-48 overflow-hidden">
            <!-- Replace with Astro Image component when you add real images:
            <Image
              src={blog1Img}
              alt="The Future of Web Development"
              class="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
              width={800}
              height={400}
            />
            -->
            <img
              src="/images/placeholder-800x600.svg"
              alt="The Future of Web Development"
              class="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
              width="800"
              height="400"
            />
          </div>
          <div class="p-6">
            <div class="flex items-center text-sm text-gray-500 mb-3">
              <time datetime="2024-01-15">January 15, 2024</time>
              <span class="mx-2">•</span>
              <span>5 min read</span>
            </div>
            <h2 class="text-xl font-semibold text-gray-900 mb-3 hover:text-blue-600 transition-colors">
              <a href="#">The Future of Web Development: Trends to Watch in 2024</a>
            </h2>
            <p class="text-gray-600 mb-4">
              Explore the latest trends and technologies shaping the future of web development, from AI integration to new frameworks.
            </p>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="bg-gray-300 w-8 h-8 rounded-full mr-3"></div>
                <span class="text-sm text-gray-700">John Doe</span>
              </div>
              <Button href="#" variant="outline" size="sm">
                Read More
              </Button>
            </div>
          </div>
        </article>

        <!-- Blog Post 2 -->
        <article class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-lg transition-shadow">
          <div class="h-48 overflow-hidden">
            <img
              src="/images/placeholder-800x600.svg"
              alt="UX Design Principles"
              class="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
              width="800"
              height="400"
            />
          </div>
          <div class="p-6">
            <div class="flex items-center text-sm text-gray-500 mb-3">
              <time datetime="2024-01-10">January 10, 2024</time>
              <span class="mx-2">•</span>
              <span>7 min read</span>
            </div>
            <h2 class="text-xl font-semibold text-gray-900 mb-3 hover:text-blue-600 transition-colors">
              <a href="#">10 Essential UX Design Principles for Better User Experience</a>
            </h2>
            <p class="text-gray-600 mb-4">
              Learn the fundamental UX design principles that will help you create more intuitive and user-friendly interfaces.
            </p>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="bg-gray-300 w-8 h-8 rounded-full mr-3"></div>
                <span class="text-sm text-gray-700">Jane Smith</span>
              </div>
              <Button href="#" variant="outline" size="sm">
                Read More
              </Button>
            </div>
          </div>
        </article>

        <!-- Blog Post 3 -->
        <article class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow">
          <div class="bg-gray-200 h-48 flex items-center justify-center">
            <p class="text-gray-500">Blog Image</p>
          </div>
          <div class="p-6">
            <div class="flex items-center text-sm text-gray-500 mb-3">
              <time datetime="2024-01-05">January 5, 2024</time>
              <span class="mx-2">•</span>
              <span>4 min read</span>
            </div>
            <h2 class="text-xl font-semibold text-gray-900 mb-3 hover:text-blue-600 transition-colors">
              <a href="#">SEO Best Practices for Modern Websites</a>
            </h2>
            <p class="text-gray-600 mb-4">
              Discover the latest SEO strategies and best practices to improve your website's search engine rankings.
            </p>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="bg-gray-300 w-8 h-8 rounded-full mr-3"></div>
                <span class="text-sm text-gray-700">Mike Johnson</span>
              </div>
              <Button href="#" variant="outline" size="sm">
                Read More
              </Button>
            </div>
          </div>
        </article>

        <!-- Blog Post 4 -->
        <article class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow">
          <div class="bg-gray-200 h-48 flex items-center justify-center">
            <p class="text-gray-500">Blog Image</p>
          </div>
          <div class="p-6">
            <div class="flex items-center text-sm text-gray-500 mb-3">
              <time datetime="2023-12-28">December 28, 2023</time>
              <span class="mx-2">•</span>
              <span>6 min read</span>
            </div>
            <h2 class="text-xl font-semibold text-gray-900 mb-3 hover:text-blue-600 transition-colors">
              <a href="#">Building Responsive Websites with Tailwind CSS</a>
            </h2>
            <p class="text-gray-600 mb-4">
              Learn how to create beautiful, responsive websites using Tailwind CSS utility classes and best practices.
            </p>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="bg-gray-300 w-8 h-8 rounded-full mr-3"></div>
                <span class="text-sm text-gray-700">Sarah Wilson</span>
              </div>
              <Button href="#" variant="outline" size="sm">
                Read More
              </Button>
            </div>
          </div>
        </article>

        <!-- Blog Post 5 -->
        <article class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow">
          <div class="bg-gray-200 h-48 flex items-center justify-center">
            <p class="text-gray-500">Blog Image</p>
          </div>
          <div class="p-6">
            <div class="flex items-center text-sm text-gray-500 mb-3">
              <time datetime="2023-12-20">December 20, 2023</time>
              <span class="mx-2">•</span>
              <span>8 min read</span>
            </div>
            <h2 class="text-xl font-semibold text-gray-900 mb-3 hover:text-blue-600 transition-colors">
              <a href="#">The Complete Guide to Digital Marketing Strategy</a>
            </h2>
            <p class="text-gray-600 mb-4">
              A comprehensive guide to creating and implementing an effective digital marketing strategy for your business.
            </p>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="bg-gray-300 w-8 h-8 rounded-full mr-3"></div>
                <span class="text-sm text-gray-700">Alex Chen</span>
              </div>
              <Button href="#" variant="outline" size="sm">
                Read More
              </Button>
            </div>
          </div>
        </article>

        <!-- Blog Post 6 -->
        <article class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow">
          <div class="bg-gray-200 h-48 flex items-center justify-center">
            <p class="text-gray-500">Blog Image</p>
          </div>
          <div class="p-6">
            <div class="flex items-center text-sm text-gray-500 mb-3">
              <time datetime="2023-12-15">December 15, 2023</time>
              <span class="mx-2">•</span>
              <span>5 min read</span>
            </div>
            <h2 class="text-xl font-semibold text-gray-900 mb-3 hover:text-blue-600 transition-colors">
              <a href="#">Mobile-First Design: Why It Matters in 2024</a>
            </h2>
            <p class="text-gray-600 mb-4">
              Understanding the importance of mobile-first design and how to implement it effectively in your projects.
            </p>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="bg-gray-300 w-8 h-8 rounded-full mr-3"></div>
                <span class="text-sm text-gray-700">Emma Davis</span>
              </div>
              <Button href="#" variant="outline" size="sm">
                Read More
              </Button>
            </div>
          </div>
        </article>
      </div>

      <!-- Pagination -->
      <div class="flex justify-center mt-12">
        <nav class="flex items-center space-x-2">
          <Button variant="outline" size="sm" disabled>
            Previous
          </Button>
          <Button size="sm">1</Button>
          <Button variant="outline" size="sm">2</Button>
          <Button variant="outline" size="sm">3</Button>
          <Button variant="outline" size="sm">
            Next
          </Button>
        </nav>
      </div>
    </div>
  </section>
</MainLayout>
