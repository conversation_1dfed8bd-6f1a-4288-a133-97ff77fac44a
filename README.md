# Astro Template - Basic Reusable Template

A clean, professional Astro template with Tailwind CSS, perfect for building simple 5-6 page websites. This template includes layouts, components, and basic SEO setup for quick project starts.

## ✨ Features

-   **Modern Stack**: Astro + Tailwind CSS
-   **Responsive Design**: Mobile-first approach with Tailwind utilities
-   **SEO Ready**: Meta tags, Open Graph, Twitter Cards, and structured data
-   **Reusable Components**: Header, Footer, Navigation, Button, and SEO components
-   **Multiple Layouts**: Base layout and Main layout with header/footer
-   **Sample Pages**: Home, About, Services, Portfolio, Blog, Contact, Privacy, Terms
-   **Custom CSS & JS**: Empty files ready for your custom styling and functionality
-   **Static Site Generation**: Optimized for fast loading and deployment

## 🚀 Quick Start

1. **Install dependencies**

    ```bash
    npm install
    ```

2. **Start development server**

    ```bash
    npm run dev
    ```

3. **Build for production**
    ```bash
    npm run build
    ```

## 📁 Project Structure

```text
/
├── public/
│   ├── favicon.svg
│   ├── og-image.jpg          # Social media image placeholder
│   ├── styles/
│   │   └── main.css          # Custom CSS file
│   └── scripts/
│       └── main.js           # Custom JavaScript file
├── src/
│   ├── components/
│   │   ├── Button.astro      # Reusable button component
│   │   ├── Footer.astro      # Site footer
│   │   ├── Header.astro      # Site header
│   │   ├── Navigation.astro  # Navigation menu
│   │   └── SEO.astro         # SEO meta tags component
│   ├── layouts/
│   │   ├── BaseLayout.astro  # Base HTML structure with SEO
│   │   └── MainLayout.astro  # Main layout with header/footer
│   └── pages/
│       ├── index.astro       # Homepage
│       ├── about.astro       # About page
│       ├── services.astro    # Services page
│       ├── portfolio.astro   # Portfolio page
│       ├── blog.astro        # Blog listing page
│       ├── contact.astro     # Contact page
│       ├── privacy.astro     # Privacy policy
│       └── terms.astro       # Terms of service
├── astro.config.mjs          # Astro configuration
└── package.json
```

## 🎨 Customization

### 1. Update Site Information

Edit `src/layouts/BaseLayout.astro` to update:

-   Site title and description
-   Social media image
-   Canonical URL
-   Meta tags

### 2. Customize Navigation

Edit `src/components/Navigation.astro` to modify menu items:

```javascript
const navItems = [
    { name: "Home", href: "/" },
    { name: "About", href: "/about" },
    // Add or remove items as needed
];
```

### 3. Update Contact Information

Edit footer and contact page with your actual:

-   Address
-   Phone number
-   Email
-   Social media links

### 4. Add Custom Styling

-   Add custom CSS to `public/styles/main.css`
-   Modify Tailwind classes in components
-   Add custom JavaScript to `public/scripts/main.js`

### 5. Replace Placeholder Content

-   Update all "Your Company" references
-   Replace placeholder images
-   Add real content to all pages
-   Update the OG image (`public/og-image.jpg`)

## 🛠️ Commands

| Command             | Action                                           |
| :------------------ | :----------------------------------------------- |
| `npm install`       | Installs dependencies                            |
| `npm run dev`       | Starts local dev server at `localhost:4321`      |
| `npm run build`     | Build your production site to `./dist/`          |
| `npm run preview`   | Preview your build locally, before deploying     |
| `npm run astro ...` | Run CLI commands like `astro add`, `astro check` |

## 📝 Usage Tips

1. **For New Projects**: Clone this template and customize the content, colors, and branding
2. **Page Creation**: Add new pages in `src/pages/` - they'll automatically become routes
3. **Component Reuse**: Use existing components like `Button.astro` throughout your pages
4. **SEO Optimization**: Each page uses the layout system with proper meta tags
5. **Mobile Responsive**: All components are built mobile-first with Tailwind CSS

## 🚀 Deployment

This template is configured for static site generation. You can deploy to:

-   Netlify
-   Vercel
-   GitHub Pages
-   Any static hosting service

Just run `npm run build` and deploy the `dist/` folder.

## 📚 Learn More

-   [Astro Documentation](https://docs.astro.build)
-   [Tailwind CSS Documentation](https://tailwindcss.com/docs)
-   [Astro Discord](https://astro.build/chat)
